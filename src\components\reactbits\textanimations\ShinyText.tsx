import React from 'react';
import { useCurrentFrame, interpolate } from 'remotion';

interface ShinyTextProps {
    text: string;
    disabled?: boolean;
    className?: string;
    // Remotion Frame 控制参数
    startFrame?: number; // 动画开始帧
    endFrame?: number; // 动画结束帧
    duration?: number; // 动画持续时间（帧数）
    loop?: boolean; // 是否循环播放
    // 动画起始位置控制
    startPosition?: number; // 动画起始位置（100到0，默认100从左上开始）
    endPosition?: number; // 动画结束位置（0到100，默认0到右下结束）
    // 颜色反转选项
    inverted?: boolean; // 是否反转颜色（深色文字配浅色闪光）
}

const ShinyText: React.FC<ShinyTextProps> = ({
    text,
    disabled = false,
    className = '',
    startFrame = 0,
    endFrame,
    duration = 60, // 闪光扫过的持续时间（帧数）
    loop = true,
    startPosition = 100, // 默认从右侧开始
    endPosition = 0, // 默认到左侧结束
    inverted = false, // 默认不反转颜色
}) => {
    const frame = useCurrentFrame();

    // 计算动画参数（基于帧控制）
    const getBackgroundPosition = () => {
        // 简化逻辑：直接使用 duration 作为动画持续时间
        const animationEndFrame = startFrame + duration;

        // 检查是否在动画范围内
        if (frame < startFrame) {
            return `${startPosition}% 0%`; // 动画未开始，显示起始位置
        }

        // 非循环模式下，如果超过或等于动画结束帧，不应该继续计算动画
        if (!loop && frame >= animationEndFrame) {
            return `${endPosition}% 0%`; // 动画已结束（非循环模式），显示结束位置
        }

        // 计算动画进度
        let animationFrame = frame;
        if (loop && frame >= animationEndFrame) {
            // 循环模式：重复动画
            const cycleDuration = animationEndFrame - startFrame;
            animationFrame = startFrame + ((frame - startFrame) % cycleDuration);
        }

        // 直接计算背景位置：在完整的 duration 帧内从 startPosition 扫到 endPosition
        const backgroundPosition = interpolate(
            animationFrame,
            [startFrame, animationEndFrame], // 使用完整的动画范围
            [startPosition, endPosition]
        );

        return `${backgroundPosition}% 0%`;
    };

    // 检查动画状态 - 使用与 getBackgroundPosition 相同的逻辑
    const animationNaturalEndFrame = startFrame + duration;

    const isBeforeAnimation = frame < startFrame;
    const isAfterAnimationNaturalEnd = frame >= animationNaturalEndFrame;

    // 根据反转状态选择静态文本颜色
    const staticTextColor = inverted ? 'text-[#4a4a4a]' : 'text-[#b5b5b5a4]';

    // 如果禁用动画或动画未开始，显示静态文本
    if (disabled || isBeforeAnimation) {
        return (
            <div className={`${staticTextColor} inline-block ${className}`}>
                {text}
            </div>
        );
    }

    // 如果是非循环模式且超过了动画自然结束帧，显示静态文本
    if (!loop && isAfterAnimationNaturalEnd) {
        return (
            <div className={`${staticTextColor} inline-block ${className}`}>
                {text}
            </div>
        );
    }

    // 只有在动画进行中时才计算背景位置
    const backgroundPosition = getBackgroundPosition();
    console.log(backgroundPosition);

    // 根据反转状态选择渐变颜色和文本颜色
    const gradientColor = inverted
        ? 'linear-gradient(120deg, rgba(0, 0, 0, 0) 40%, rgba(0, 0, 0, 0.8) 50%, rgba(0, 0, 0, 0) 60%)'
        : 'linear-gradient(120deg, rgba(255, 255, 255, 0) 40%, rgba(255, 255, 255, 0.8) 50%, rgba(255, 255, 255, 0) 60%)';

    const animatedTextColor = inverted ? 'text-[#4a4a4a]' : 'text-[#b5b5b5a4]';

    return (
        <div
            className={`${animatedTextColor} bg-clip-text inline-block ${className}`}
            style={{
                backgroundImage: gradientColor,
                backgroundSize: '400% 100%', // 增加背景大小以匹配位置范围
                WebkitBackgroundClip: 'text',
                backgroundClip: 'text',
                backgroundPosition: backgroundPosition,
            }}
        >
            {text}
        </div>
    );
};

export default ShinyText;