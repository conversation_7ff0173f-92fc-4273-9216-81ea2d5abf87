import React from 'react';
import { AbsoluteFill } from 'remotion';
import { SceneProps } from '../types';
import ShinyText from '../components/reactbits/textanimations/ShinyText';
import Subtitle from '../components/Subtitle';

const OpeningScene: React.FC<SceneProps> = ({ frame, durationInFrames }) => {

  return (
    <AbsoluteFill className="flex flex-col items-center justify-center text-white relative">
      {/* 主标题区域 */}
      <div className="relative z-10 text-center max-w-9xl px-8">
        {/* 主标题 - 使用弹性动画 */}
        <ShinyText
          text="早睡真的有用吗？"
          className="text-9xl font-semibold text-center"
          startFrame={0}
          endFrame={60}
          duration={60}
          loop={true}
          startPosition={0}
          endPosition={100}
          inverted={true}
        />
      </div>

      {/* 字幕区域 - 底部显示 */}
      <div className="absolute bottom-16 left-0 right-0 z-10 flex justify-center px-8">
        <Subtitle
          text="很多人相信：只要早睡，就能变健康、变聪明"
          startFrame={60}
          duration={120}
        />
      </div>
    </AbsoluteFill>
  );
};

export default OpeningScene;